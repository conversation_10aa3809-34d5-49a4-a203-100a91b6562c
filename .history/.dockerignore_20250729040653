# Environment files
.env
.env.*

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
*.md
docs/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# Large files that shouldn't be in Docker
*.zip
*.tar.gz
*.rar
*.7z

# Deployment files (keep only what's needed)
deploy_cloud_run.sh
cloud_run_config.yaml
CLOUD_RUN_DEPLOY.md
DEPLOYMENT_SUMMARY.md