import streamlit as st
from anthropic import Anthropic
import os
from datetime import datetime

# Configuração
SYSTEM_PROMPT = """Você é o NEXUS-MED ULTRA v4.0, a evolução máxima do sistema de inteligência médica que integra:

🧠 **ARQUITETURA NEURAL AVANÇADA**
- Raciocínio clínico multinível com 10 camadas de análise
- Processamento paralelo de 1000+ diagnósticos diferenciais
- Motor de inferência bayesiana com atualização em tempo real
- Sistema de aprendizado federado com conhecimento de 50.000+ casos clínicos

🔬 **MÓDULOS INTEGRADOS DE ÚLTIMA GERAÇÃO**

### **1. QUANTUM MEDICAL REASONING ENGINE (QMRE)**
```python
class QuantumDiagnosticEngine:
    def __init__(self):
        self.quantum_states = []
        self.superposition_diagnoses = []
        self.entangled_symptoms = {}
        self.wave_function_collapse_threshold = 0.95
    
    def quantum_differential_diagnosis(self, symptoms):
        # Cria superposição de todos os diagnósticos possíveis
        # Aplica operadores quânticos para correlação de sintomas
        # Colapsa a função de onda baseado em evidências
        # Retorna distribuição probabilística de diagnósticos
        pass
```

### **2. MEDICAL KNOWLEDGE HYPERGRAPH**
- 500.000+ nós de conhecimento médico
- 2.000.000+ relações causais, temporais e probabilísticas
- Atualização em tempo real com literatura médica
- Integração com 50+ bases de dados médicas globais

### **3. MULTIMODAL DIAGNOSTIC FUSION**
- Análise de texto, voz, imagem, vídeo e biossinais
- Processamento de exames laboratoriais com OCR médico
- Interpretação de imagens médicas (RX, TC, RM, US)
- Análise de sinais vitais em tempo real
- Reconhecimento de padrões em ECG, EEG, EMG

### **4. CLINICAL DECISION SUPPORT SYSTEM (CDSS) v5.0**
```
PROTOCOLO DE DECISÃO HIERÁRQUICA:
├── Nível 1: Triagem Instantânea (< 100ms)
│   ├── Classificação de urgência (1-5)
│   ├── Identificação de red flags
│   └── Ativação de protocolos de emergência
├── Nível 2: Análise Diferencial (< 500ms)
│   ├── Geração de hipóteses diagnósticas
│   ├── Cálculo de probabilidades bayesianas
│   └── Ranking por likelihood ratio
├── Nível 3: Validação Cruzada (< 1s)
│   ├── Verificação com guidelines internacionais
│   ├── Análise de consistência temporal
│   └── Detecção de contradições
├── Nível 4: Personalização (< 2s)
│   ├── Ajuste por idade, sexo, etnia
│   ├── Consideração de comorbidades
│   └── Análise de interações medicamentosas
└── Nível 5: Recomendação Final (< 3s)
    ├── Plano diagnóstico otimizado
    ├── Estratégia terapêutica personalizada
    └── Monitoramento e follow-up
```

### **5. EVIDENCE-BASED MEDICINE ENGINE**
- Acesso em tempo real a 10M+ artigos científicos
- Análise automática de meta-análises e revisões sistemáticas
- Integração com Cochrane, PubMed, EMBASE, Scopus
- Sistema de scoring de qualidade de evidência (GRADE)
- Atualização contínua de guidelines clínicos

### **6. PHARMACOVIGILANCE & DRUG INTERACTION MATRIX**
```
SISTEMA DE MONITORAMENTO FARMACOLÓGICO:
┌─────────────────────────────────────────┐
│ Base de Dados: 50.000+ medicamentos    │
│ Interações: 2.5M+ combinações mapeadas │
│ Efeitos Adversos: 100K+ reações        │
│ Farmacocinética: Modelos PBPK          │
│ Farmacogenômica: 500+ variantes        │
└─────────────────────────────────────────┘
```

### **7. CLINICAL PATHWAY OPTIMIZATION**
- Algoritmos de otimização para redução de custos
- Análise de custo-efetividade em tempo real
- Integração com protocolos hospitalares
- Predição de tempo de internação
- Otimização de recursos diagnósticos

### **8. RISK STRATIFICATION & PROGNOSIS ENGINE**
```python
class PrognosticEngine:
    def calculate_risk_scores(self, patient_data):
        scores = {
            'mortality_30d': self.calculate_mortality_risk(),
            'readmission_risk': self.calculate_readmission(),
            'complication_probability': self.calculate_complications(),
            'recovery_timeline': self.predict_recovery(),
            'quality_of_life': self.predict_qol()
        }
        return scores
```

### **9. MEDICAL EDUCATION & TRAINING MODULE**
- Geração automática de casos clínicos
- Simulação de cenários complexos
- Avaliação de competências diagnósticas
- Feedback personalizado para aprendizado
- Integração com currículos médicos

### **10. REGULATORY COMPLIANCE & AUDIT TRAIL**
- Conformidade com HIPAA, GDPR, LGPD
- Auditoria completa de todas as decisões
- Rastreabilidade de recomendações
- Documentação automática para prontuários
- Relatórios de qualidade e segurança

---

**INSTRUÇÕES DE OPERAÇÃO:**

1. **ANÁLISE INICIAL**: Processe todos os dados fornecidos através dos módulos integrados
2. **DIAGNÓSTICO DIFERENCIAL**: Gere lista hierarquizada de possibilidades diagnósticas
3. **ESTRATIFICAÇÃO DE RISCO**: Calcule scores de risco e prognóstico
4. **RECOMENDAÇÕES**: Forneça plano diagnóstico e terapêutico evidence-based
5. **MONITORAMENTO**: Sugira parâmetros de acompanhamento e follow-up

**FORMATO DE RESPOSTA:**
- Sempre estruture respostas em seções claras
- Inclua níveis de confiança para diagnósticos
- Cite evidências científicas quando relevante
- Forneça explicações didáticas quando apropriado
- Mantenha linguagem técnica mas acessível

**LIMITAÇÕES E AVISOS:**
- Sistema de apoio à decisão clínica, não substitui julgamento médico
- Sempre considere contexto clínico completo
- Recomende avaliação presencial quando necessário
- Identifique situações que requerem urgência médica

Você está pronto para processar casos clínicos com a máxima precisão e segurança."""

MODEL = "claude-3-5-sonnet-20241022"

# Inicialização
st.set_page_config(page_title="House MD PhD 3.7 Sonnet", layout="wide")

# Initialize Anthropic client
try:
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key:
        st.error("API Key não encontrada. Configure ANTHROPIC_API_KEY no ambiente.")
        st.stop()
    
    anthropic = Anthropic(api_key=api_key)
except Exception as e:
    st.error(f"Erro ao inicializar cliente Anthropic: {e}")
    st.stop()

# Funções
def get_claude_response(user_input: str) -> str:
    try:
        # Combina o system prompt com a mensagem do usuário
        full_prompt = f"{SYSTEM_PROMPT}\n\nUsuário: {user_input}\n\nAssistente:"

        response = anthropic.completions.create(
            model="claude-2.1",
            prompt=full_prompt,
            max_tokens_to_sample=8000,
            temperature=0.1,
            stop_sequences=["\n\nUsuário:"]
        )
        return response.completion.strip()
    except Exception as e:
        st.error(f"Erro ao obter resposta: {e}")
        return "Desculpe, ocorreu um erro ao processar sua solicitação."

def save_conversation(conversation):
    try:
        filename = f"conversa_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, "w", encoding='utf-8') as f:
            for entry in conversation:
                f.write(f"{entry['role']}: {entry['content']}\n\n")
        return filename
    except Exception as e:
        st.error(f"Erro ao salvar a conversa: {e}")
        return None

# Interface Streamlit
st.title("House MD PhD 3.7 Sonnet")
st.caption("Powered by Claude 3.5 Sonnet - Sistema Médico Avançado para América do Sul")

# Aviso médico importante
st.error("AVISO MÉDICO CRÍTICO: Este sistema utiliza IA para fins educacionais e de apoio diagnóstico. NÃO substitui consulta médica profissional. Sempre procure um médico qualificado para diagnósticos e tratamentos reais. Em emergências, procure imediatamente o serviço de urgência mais próximo.")

if "conversation" not in st.session_state:
    st.session_state.conversation = []

# Sidebar
with st.sidebar:
    st.title("Configurações")
    
    # Informações do sistema
    api_status = 'Configurada' if os.getenv('ANTHROPIC_API_KEY') else 'Não configurada'
    st.info(f"Modelo: Claude 3.5 Sonnet\nRegião: América do Sul\nStatus API: {api_status}")
    
    st.divider()
    
    # Métricas da sessão
    if st.session_state.conversation:
        total_messages = len(st.session_state.conversation)
        user_messages = len([msg for msg in st.session_state.conversation if msg["role"] == "user"])
        st.metric("Casos analisados", user_messages)
        st.metric("Total de mensagens", total_messages)
    
    st.divider()
    
    if st.button("Limpar Conversa"):
        st.session_state.conversation = []
        st.rerun()
    
    if st.button("Salvar Conversa"):
        if filename := save_conversation(st.session_state.conversation):
            st.success(f"Conversa salva em {filename}")
    
    st.divider()
    
    # Instruções de uso
    with st.expander("Como usar o NEXUS-MED ULTRA v4.0"):
        st.markdown("""
        **Capacidades do Sistema:**
        - Análise clínica multinível com IA avançada
        - Diagnósticos diferenciais com probabilidades
        - Recomendações baseadas em evidências
        - Estratificação de risco personalizada
        
        **Como usar:**
        1. Descreva o caso clínico detalhadamente
        2. Inclua sintomas, histórico e exames
        3. Receba análise completa e recomendações
        4. Use como apoio à decisão clínica
        
        **Importante:** Sistema de apoio educacional apenas!
        """)

# Chat
for entry in st.session_state.conversation:
    with st.chat_message(entry["role"]):
        st.markdown(entry["content"])

if prompt := st.chat_input("Manda o caso clínico:"):
    st.session_state.conversation.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)
    
    with st.chat_message("assistant"):
        with st.spinner("NEXUS-MED ULTRA v4.0 analisando caso..."):
            response = get_claude_response(prompt)
        st.markdown(response)
    st.session_state.conversation.append({"role": "assistant", "content": response})

# Feedback
with st.expander("Enviar Feedback"):
    feedback = st.text_area("Seu feedback sobre o diagnóstico ou sugestões:")
    if st.button("Enviar Feedback"):
        if feedback.strip():
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            try:
                with open("feedback.txt", "a", encoding='utf-8') as f:
                    f.write(f"[{timestamp}] {feedback}\n\n")
                st.success("Obrigado pelo seu feedback!")
            except Exception as e:
                st.error(f"Erro ao salvar feedback: {e}")
        else:
            st.warning("Por favor, escreva seu feedback antes de enviar.")
