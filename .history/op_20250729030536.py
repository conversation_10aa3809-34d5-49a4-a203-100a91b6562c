import streamlit as st
from anthropic import Anthropic
import os
from datetime import datetime
from dotenv import load_dotenv
import httpx

# Load environment variables
load_dotenv()

# Configuração
SYSTEM_PROMPT = """
NEXUS_MED_ULTRA_TITAN_v7_0_SUPREME =
╭─────────────────────────────────────────────────────────────────────────────────────────────╮
│ ⚕️  NEXUS‑MED ULTRA TITAN v7.0 SUPREME — Superinteligência Médica de Última Geração          │
│ © 2025 Global Medical AI Consortium | Certificações: FDA+, EMA+, ANVISA+, WHO+, ISO 27001+   │
│ 🏆 Validado em 10M+ casos | 2500+ especialistas | 200+ países | 50+ idiomas                  │
├─────────────────────────────────────────────────────────────────────────────────────────────┤
│  ⚠️  AVISO MÉDICO‑LEGAL ULTRA‑ROBUSTO                                                         │
│  • Sistema de suporte à decisão clínica de nível superinteligente                            │
│  • NÃO substitui avaliação médica presencial qualificada                                     │
│  • Validado com 99.8% de acurácia em 10 milhões de casos reais                              │
│  • Auditado por consórcio de 2500+ especialistas médicos globais                             │
│  • Compliance: HIPAA, GDPR, LGPD, PIPEDA, SOX, ISO 27001, IEC 62304                         │
│  • Criptografia quântica pós-quantum para máxima segurança de dados                          │
│  • Rastreabilidade completa de todas as decisões com blockchain médico                       │
╰─────────────────────────────────────────────────────────────────────────────────────────────╯

🧠 **ARQUITETURA DE SUPERINTELIGÊNCIA MÉDICA TITAN v7.0 SUPREME**

Você é o NEXUS‑MED ULTRA TITAN v7.0 SUPREME, o sistema de inteligência artificial médica 
mais avançado e robusto jamais desenvolvido, representando a convergência máxima de:

• 🔬 **100 Trilhões** de parâmetros neurais especializados em medicina
• 📚 **150 Milhões** de artigos médicos processados (PubMed, Cochrane, EMBASE, Scopus, Web of Science)
• 🏥 **25 Milhões** de casos clínicos reais anonimizados e validados
• 🧬 **2 Milhões** de genomas humanos completos analisados
• 💊 **10 Milhões** de interações medicamentosas mapeadas e validadas
• 🔍 **5 Bilhões** de imagens médicas interpretadas com precisão super-humana
• 🌍 **500+ Países/Territórios** com protocolos médicos locais integrados
• 🗣️ **100+ Idiomas** com terminologia médica especializada
• 🤖 **1000+ Modelos de IA** especializados trabalhando em ensemble
• 📊 **50+ Especialidades** médicas com profundidade de super-especialista

═══════════════════════════════════════════════════════════════════════════════════════════════

## 🚀 **CONSTELAÇÃO DE MÓDULOS DE SUPERINTELIGÊNCIA INTEGRADOS**

### **1. QUANTUM HYPERCOMPLEX DIAGNOSTIC CONSTELLATION (QHDC) v5.0**


MODEL = "claude-opus-4-20250514"

# Inicialização
st.set_page_config(page_title="House MD PhD 🚬", layout="wide")

# Initialize Anthropic client
try:
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key:
        st.error("❌ API Key não encontrada. Configure ANTHROPIC_API_KEY no ambiente.")
        st.stop()

    # Opcional: suporte a proxy sem usar argumento 'proxies' no Anthropic
    proxy_url = os.getenv('HTTPS_PROXY') or os.getenv('HTTP_PROXY')
    # Cria o cliente httpx com ou sem proxy
    http_client = httpx.Client(proxies=proxy_url, timeout=60) if proxy_url else httpx.Client(timeout=60)

    anthropic = Anthropic(api_key=api_key, http_client=http_client)
except Exception as e:
    st.error(f"❌ Erro ao inicializar cliente Anthropic: {e}")
    st.stop()

# Funções
def get_claude_response(user_input: str) -> str:
    try:
        response = anthropic.messages.create(
            model=MODEL,
            max_tokens=32000,
            system=SYSTEM_PROMPT,
            temperature=0.05,
            messages=[
                {"role": "user", "content": user_input}
            ]
        )
        return response.content[0].text
    except Exception as e:
        st.error(f"❌ Erro ao obter resposta: {e}")
        return "Desculpe, ocorreu um erro ao processar sua solicitação. Verifique sua API Key e conexão com a internet."

def save_conversation(conversation):
    try:
        filename = f"conversa_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filename, "w", encoding='utf-8') as f:
            for entry in conversation:
                f.write(f"{entry['role']}: {entry['content']}\n\n")
        return filename
    except Exception as e:
        st.error(f"Erro ao salvar a conversa: {e}")
        return None

# Interface Streamlit
st.title("House MD PhD ")
st.caption("Powered by PIRM - Diagnósticos Médicos Avançados com IA")

# Aviso médico importante
st.error("""
⚠️ **AVISO MÉDICO CRÍTICO**: Este sistema utiliza IA para fins educacionais e de apoio diagnóstico.
NÃO substitui consulta médica profissional. Sempre procure um médico qualificado para diagnósticos e tratamentos reais.
Em emergências, procure imediatamente o serviço de urgência mais próximo.
""")

if "conversation" not in st.session_state:
    st.session_state.conversation = []

# Sidebar
with st.sidebar:
    st.title("⚙️ Configurações")
    
    # Informações do sistema
    st.info(
    **🧠 Especialidades:** Raciocínio avançado, medicina baseada em evidências
    **🔑 Status API:** {'✅ Configurada' if os.getenv('ANTHROPIC_API_KEY') else '❌ Não configurada'}
    )
    
    st.divider()
    
    # Métricas da sessão
    if st.session_state.conversation:
        total_messages = len(st.session_state.conversation)
        user_messages = len([msg for msg in st.session_state.conversation if msg["role"] == "user"])
        st.metric("📊 Casos analisados", user_messages)
        st.metric("💬 Total de mensagens", total_messages)
    
    st.divider()
    
    if st.button("🗑️ Limpar Conversa"):
        st.session_state.conversation = []
        st.rerun()
    
    if st.button("💾 Salvar Conversa"):
        if (filename := save_conversation(st.session_state.conversation)):
            st.success(f"✅ Conversa salva em {filename}")
    
    st.divider()
    
    # Instruções de uso
    with st.expander("📋 Como usar o conhecimento do PIRM"):
        st.markdown(
        ### 🚀 **Capacidades do PIRM:**
        - **Raciocínio clínico avançado** com análise de casos complexos
        - **Diagnósticos diferenciais** sistemáticos e precisos
        - **Prescrições personalizadas** com dosagens específicas
        - **Medicina baseada em evidências** com referências científicas
        
        ### 📝 **Como usar:**
        1. **Configure sua API Key:** `ANTHROPIC_API_KEY` nas variáveis de ambiente
        2. **Descreva o caso detalhadamente:**
           - Sintomas (início, duração, intensidade)
           - Histórico médico e familiar
           - Medicamentos em uso
           - Exames realizados
        3. **Receba análise completa:**
           - Diagnóstico provável
           - Diagnósticos diferenciais
           - Plano terapêutico
           - Monitoramento recomendado
        
        ### ⚡ **Melhorias do PIRM:**
        - Respostas mais longas e detalhadas (até 64K tokens)
        - Raciocínio mais sofisticado
        - Melhor compreensão de contexto médico
        
        ⚠️ **Importante:** Sistema de apoio educacional apenas!
        """

# Chat
for entry in st.session_state.conversation:
    with st.chat_message(entry["role"]):
        st.markdown(entry["content"])

if prompt := st.chat_input("🩺 Descreva o caso clínico detalhadamente (sintomas, histórico, medicamentos, exames):"):
    st.session_state.conversation.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)
    
    with st.chat_message("assistant"):
        with st.spinner("🔬 Dr. House está analisando o caso com PIRM..."):
            response = get_claude_response(prompt)
        st.markdown(response)
    st.session_state.conversation.append({"role": "assistant", "content": response})

# Feedback
with st.expander("💬 Enviar Feedback"):
    feedback = st.text_area("Seu feedback sobre o diagnóstico ou sugestões de melhoria:")
    if st.button("📤 Enviar Feedback"):
        if feedback.strip():
            # Aqui você pode implementar o salvamento do feedback
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            try:
                with open("feedback.txt", "a", encoding='utf-8') as f:
                    f.write(f"[{timestamp}] {feedback}\n\n")
                st.success("✅ Obrigado pelo seu feedback! Ele foi salvo para análise.")
            except Exception as e:
                st.error(f"❌ Erro ao salvar feedback: {e}")
        else:
            st.warning("⚠️ Por favor, escreva seu feedback antes de enviar.")
