FROM python:3.9-slim

WORKDIR /app

# Set environment variables
ENV PORT=8501
ENV PYTHONUNBUFFERED=1

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY op.py .
COPY *.py .

# Expose port
EXPOSE 8501

# Run the application
CMD ["streamlit", "run", "op.py", "--server.port=8501", "--server.address=0.0.0.0"]