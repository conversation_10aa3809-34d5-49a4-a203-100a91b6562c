steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/sonnet-med:$COMMIT_SHA', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/sonnet-med:$COMMIT_SHA']
  
  # Tag latest
  - name: 'gcr.io/cloud-builders/docker'
    args: ['tag', 'gcr.io/$PROJECT_ID/sonnet-med:$COMMIT_SHA', 'gcr.io/$PROJECT_ID/sonnet-med:latest']
  
  # Push latest tag
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/sonnet-med:latest']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
    - 'run'
    - 'deploy'
    - 'sonnet-med'
    - '--image'
    - 'gcr.io/$PROJECT_ID/sonnet-med:$COMMIT_SHA'
    - '--region'
    - 'us-central1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '2Gi'
    - '--cpu'
    - '2'
    - '--timeout'
    - '300'
    - '--max-instances'
    - '10'
    - '--min-instances'
    - '1'
    - '--set-env-vars'
    - 'ANTHROPIC_API_KEY=$_ANTHROPIC_API_KEY'

# Store images in Google Container Registry
images:
  - 'gcr.io/$PROJECT_ID/sonnet-med:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/sonnet-med:latest'

# Build timeout
timeout: '1200s'

# Substitutions (set these in Cloud Build trigger or command line)
substitutions:
  _ANTHROPIC_API_KEY: 'YOUR_API_KEY_HERE'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'