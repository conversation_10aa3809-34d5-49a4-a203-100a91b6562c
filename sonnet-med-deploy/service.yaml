apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: sonnet-med
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/launch-stage: GA
spec:
  template:
    metadata:
      annotations:
        # Maximum number of instances
        autoscaling.knative.dev/maxScale: '10'
        # Minimum number of instances
        autoscaling.knative.dev/minScale: '1'
        # CPU allocation
        run.googleapis.com/cpu-throttling: 'false'
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      serviceAccountName: sonnet-med-sa
      containers:
      - name: sonnet-med
        image: gcr.io/PROJECT_ID/sonnet-med:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: anthropic-api-key
              key: api-key
        - name: PORT
          value: '8080'
        - name: STREAMLIT_SERVER_PORT
          value: '8080'
        - name: STREAMLIT_SERVER_ADDRESS
          value: '0.0.0.0'
        - name: STREAMLIT_SERVER_HEADLESS
          value: 'true'
        resources:
          limits:
            cpu: '2'
            memory: 2Gi
          requests:
            cpu: '1'
            memory: 1Gi
        startupProbe:
          httpGet:
            path: /_stcore/health
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /_stcore/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
  traffic:
  - percent: 100
    latestRevision: true