#!/bin/bash

# Sonnet Med Deployment Script for Google Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=""
REGION="us-central1"
SERVICE_NAME="sonnet-med"
ANTHROPIC_API_KEY=""

# Function to print colored output
print_color() {
    printf "${2}${1}${NC}\n"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --api-key)
            ANTHROPIC_API_KEY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: ./deploy.sh [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --project-id    GCP Project ID (required)"
            echo "  --region        GCP Region (default: us-central1)"
            echo "  --api-key       Anthropic API Key (required)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            print_color "Unknown option: $1" "$RED"
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$PROJECT_ID" ]; then
    print_color "Error: Project ID is required. Use --project-id flag." "$RED"
    exit 1
fi

if [ -z "$ANTHROPIC_API_KEY" ]; then
    print_color "Error: Anthropic API key is required. Use --api-key flag." "$RED"
    exit 1
fi

# Check if gcloud is installed
if ! command_exists gcloud; then
    print_color "Error: gcloud CLI is not installed. Please install it first." "$RED"
    exit 1
fi

print_color "🚀 Starting deployment of Sonnet Med to Cloud Run..." "$GREEN"

# Set project
print_color "Setting project to: $PROJECT_ID" "$YELLOW"
gcloud config set project $PROJECT_ID

# Enable required APIs
print_color "Enabling required APIs..." "$YELLOW"
gcloud services enable cloudbuild.googleapis.com || true
gcloud services enable run.googleapis.com || true
gcloud services enable containerregistry.googleapis.com || true

# Build options
BUILD_METHOD=""
echo ""
print_color "Choose build method:" "$YELLOW"
echo "1) Local build and push"
echo "2) Cloud Build (recommended for production)"
read -p "Enter choice (1 or 2): " choice

case $choice in
    1)
        BUILD_METHOD="local"
        ;;
    2)
        BUILD_METHOD="cloud"
        ;;
    *)
        print_color "Invalid choice. Exiting." "$RED"
        exit 1
        ;;
esac

# Copy the application file
print_color "Copying application file..." "$YELLOW"
cp ../4sonnet.py ./app.py

if [ "$BUILD_METHOD" = "local" ]; then
    # Local build
    print_color "Building Docker image locally..." "$YELLOW"
    docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/$SERVICE_NAME:latest .
    
    print_color "Configuring Docker for GCR..." "$YELLOW"
    gcloud auth configure-docker
    
    print_color "Pushing image to Container Registry..." "$YELLOW"
    docker push gcr.io/$PROJECT_ID/$SERVICE_NAME:latest
    
    print_color "Deploying to Cloud Run..." "$YELLOW"
    gcloud run deploy $SERVICE_NAME \
        --image gcr.io/$PROJECT_ID/$SERVICE_NAME:latest \
        --region $REGION \
        --platform managed \
        --allow-unauthenticated \
        --memory 2Gi \
        --cpu 2 \
        --timeout 300 \
        --max-instances 10 \
        --min-instances 1 \
        --set-env-vars "ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY"
else
    # Cloud Build
    print_color "Submitting to Cloud Build..." "$YELLOW"
    gcloud builds submit \
        --config cloudbuild.yaml \
        --substitutions="_ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY" \
        .
fi

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')

print_color "✅ Deployment completed successfully!" "$GREEN"
print_color "🌐 Service URL: $SERVICE_URL" "$GREEN"

# Cleanup
rm -f app.py

# Option to test the service
echo ""
read -p "Would you like to open the service in your browser? (y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command_exists open; then
        open "$SERVICE_URL"
    elif command_exists xdg-open; then
        xdg-open "$SERVICE_URL"
    else
        print_color "Please open the following URL in your browser: $SERVICE_URL" "$YELLOW"
    fi
fi

print_color "🎉 Deployment script completed!" "$GREEN"