# House Sonnet - Medical AI Assistant

## 🏥 Sobre o Projeto

House Sonnet é uma aplicação de inteligência artificial médica baseada no Claude Sonnet 3.5, projetada para fornecer análises médicas avançadas e suporte diagnóstico educacional.

## ⚠️ Aviso Importante

**ESTE SISTEMA É APENAS PARA FINS EDUCACIONAIS E DE APOIO DIAGNÓSTICO.**
- NÃO substitui consulta médica profissional
- Sempre procure um médico qualificado para diagnósticos e tratamentos reais
- Em emergências, procure imediatamente o serviço de urgência mais próximo

## 🚀 Funcionalidades

- **Raciocínio clínico avançado** com análise de casos complexos
- **Diagnósticos diferenciais** sistemáticos e precisos
- **Prescrições personalizadas** com dosagens específicas
- **Medicina baseada em evidências** com referências científicas
- **Interface de chat** intuitiva e responsiva
- **Streaming de respostas** para análises longas
- **Sistema de feedback** integrado

## 🛠️ Tecnologias Utilizadas

- **Python 3.11**
- **Streamlit** - Interface web
- **Anthropic Claude Sonnet 3.5** - Modelo de IA
- **Docker** - Containerização
- **Google Cloud Run** - Deploy e hospedagem

## 📦 Estrutura do Projeto

```
house-sonnet-deploy/
├── app.py              # Aplicação principal
├── requirements.txt    # Dependências Python
├── Dockerfile         # Configuração Docker
├── .dockerignore      # Arquivos ignorados no build
├── deploy.sh          # Script de deploy automatizado
└── README.md          # Documentação
```

## 🔧 Configuração Local

### Pré-requisitos

- Python 3.11+
- Docker
- Google Cloud CLI (gcloud)
- Conta no Google Cloud Platform
- API Key da Anthropic

### Instalação

1. **Clone o repositório:**
```bash
cd house-sonnet-deploy
```

2. **Configure a API Key:**
```bash
export ANTHROPIC_API_KEY="sua-api-key-aqui"
```

3. **Instale as dependências:**
```bash
pip install -r requirements.txt
```

4. **Execute localmente:**
```bash
streamlit run app.py
```

## 🚀 Deploy no Google Cloud Run

### Deploy Automatizado

Execute o script de deploy:
```bash
./deploy.sh
```

### Deploy Manual

1. **Configure o projeto:**
```bash
gcloud config set project primeversion
```

2. **Build da imagem:**
```bash
docker build --platform linux/amd64 -t gcr.io/primeversion/house-sonnet:latest .
```

3. **Push para GCR:**
```bash
docker push gcr.io/primeversion/house-sonnet:latest
```

4. **Deploy no Cloud Run:**
```bash
gcloud run deploy house-sonnet \
    --image gcr.io/primeversion/house-sonnet:latest \
    --region us-central1 \
    --platform managed \
    --allow-unauthenticated \
    --set-env-vars ANTHROPIC_API_KEY="sua-api-key" \
    --memory 2Gi \
    --cpu 2 \
    --max-instances 10 \
    --timeout 900
```

## 🔒 Configurações de Segurança

- **Timeout**: 900 segundos (15 minutos)
- **Memória**: 2GB
- **CPU**: 2 cores
- **Instâncias máximas**: 10
- **Usuário não-root** no container
- **Health checks** configurados

## 📊 Monitoramento

- **Logs**: Disponíveis no Google Cloud Logging
- **Métricas**: Monitoramento via Google Cloud Monitoring
- **Health checks**: Endpoint `/_stcore/health`

## 🤝 Como Usar

1. Acesse a URL do serviço após o deploy
2. Descreva o caso clínico detalhadamente
3. Inclua sintomas, histórico, medicamentos e exames
4. Receba análise completa com diagnósticos diferenciais
5. Use o sistema de feedback para melhorias

## 📝 Exemplo de Uso

```
Paciente masculino, 45 anos, apresenta dor torácica há 2 horas, 
irradiando para braço esquerdo, associada a sudorese e náusea. 
Histórico de hipertensão e tabagismo. PA: 160/100 mmHg, FC: 110 bpm.
ECG mostra elevação do segmento ST em DII, DIII e aVF.
```

## 🔄 Atualizações

Para atualizar o serviço:
1. Modifique o código necessário
2. Execute `./deploy.sh` novamente
3. O Cloud Run criará uma nova revisão automaticamente

## 📞 Suporte

Para questões técnicas ou sugestões de melhoria, use o sistema de feedback integrado na aplicação.

---

**Desenvolvido com ❤️ para educação médica e pesquisa**
