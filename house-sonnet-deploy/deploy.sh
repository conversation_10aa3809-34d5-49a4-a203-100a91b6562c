#!/bin/bash

# House Sonnet Deploy Script
# Automated deployment to Google Cloud Run

set -e

# Configuration
PROJECT_ID="primeversion"
SERVICE_NAME="house-sonnet"
REGION="us-central1"
API_KEY="************************************************************************************************************"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_color() {
    echo -e "${2}${1}${NC}"
}

print_color "🚀 Starting deployment of House Sonnet to Cloud Run..." "$BLUE"

# Set project
print_color "Setting project to: $PROJECT_ID" "$YELLOW"
gcloud config set project $PROJECT_ID

# Enable required APIs
print_color "Enabling required APIs..." "$YELLOW"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build and push Docker image
print_color "Building Docker image..." "$YELLOW"
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/$SERVICE_NAME:latest .

print_color "Configuring Docker for GCR..." "$YELLOW"
gcloud auth configure-docker

print_color "Pushing image to Google Container Registry..." "$YELLOW"
docker push gcr.io/$PROJECT_ID/$SERVICE_NAME:latest

# Deploy to Cloud Run
print_color "Deploying to Cloud Run..." "$YELLOW"
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME:latest \
    --region $REGION \
    --platform managed \
    --allow-unauthenticated \
    --set-env-vars ANTHROPIC_API_KEY="$API_KEY" \
    --memory 2Gi \
    --cpu 2 \
    --max-instances 10 \
    --timeout 900 \
    --port 8080

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format="value(status.url)")

print_color "✅ Deployment completed successfully!" "$GREEN"
print_color "🌐 Service URL: $SERVICE_URL" "$GREEN"
print_color "📊 Service: $SERVICE_NAME" "$GREEN"
print_color "🌍 Region: $REGION" "$GREEN"
print_color "🔧 Configuration:" "$GREEN"
print_color "   - Memory: 2GB" "$GREEN"
print_color "   - CPU: 2" "$GREEN"
print_color "   - Max instances: 10" "$GREEN"
print_color "   - Timeout: 900s" "$GREEN"
print_color "   - API Key: Configured" "$GREEN"

print_color "🎉 House Sonnet is now live and ready to use!" "$GREEN"
