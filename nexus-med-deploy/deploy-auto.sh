#!/bin/bash

# Automated deployment script
set -e

PROJECT_ID="primeversion"
API_KEY="************************************************************************************************************"
SERVICE_NAME="nexus-med"
REGION="us-central1"

echo "🚀 Starting automated deployment..."

# Set project
gcloud config set project $PROJECT_ID

# Copy application file
cp ../37.py ./app.py

# Build with correct platform
echo "Building Docker image for linux/amd64..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/$SERVICE_NAME:latest .

# Push to GCR
echo "Pushing to Container Registry..."
docker push gcr.io/$PROJECT_ID/$SERVICE_NAME:latest

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME:latest \
    --region $REGION \
    --platform managed \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 300 \
    --max-instances 10 \
    --min-instances 1 \
    --set-env-vars "ANTHROPIC_API_KEY=$API_KEY"

# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')

echo "✅ Deployment completed!"
echo "🌐 Service URL: $SERVICE_URL"

# Cleanup
rm -f app.py