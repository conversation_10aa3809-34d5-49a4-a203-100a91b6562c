#!/bin/bash

# Nexus Med - Initial Setup Script for Google Cloud Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=""
REGION="us-central1"
SERVICE_ACCOUNT_NAME="nexus-med-sa"

# Function to print colored output
print_color() {
    printf "${2}${1}${NC}\n"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --help)
            echo "Usage: ./setup.sh [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --project-id    GCP Project ID (required)"
            echo "  --region        GCP Region (default: us-central1)"
            echo "  --help          Show this help message"
            echo ""
            echo "This script will:"
            echo "  - Enable required GCP APIs"
            echo "  - Create a service account for Cloud Run"
            echo "  - Set up Container Registry"
            echo "  - Configure project defaults"
            exit 0
            ;;
        *)
            print_color "Unknown option: $1" "$RED"
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$PROJECT_ID" ]; then
    print_color "Error: Project ID is required. Use --project-id flag." "$RED"
    exit 1
fi

# Check if gcloud is installed
if ! command_exists gcloud; then
    print_color "Error: gcloud CLI is not installed." "$RED"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

print_color "🚀 Starting Nexus Med setup for GCP Project: $PROJECT_ID" "$GREEN"

# Authenticate if needed
print_color "Checking authentication..." "$YELLOW"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_color "No active account found. Please authenticate:" "$YELLOW"
    gcloud auth login
fi

# Set project
print_color "Setting project to: $PROJECT_ID" "$YELLOW"
gcloud config set project $PROJECT_ID

# Enable required APIs
print_color "Enabling required APIs..." "$YELLOW"
apis=(
    "compute.googleapis.com"
    "containerregistry.googleapis.com"
    "cloudbuild.googleapis.com"
    "run.googleapis.com"
    "secretmanager.googleapis.com"
    "cloudresourcemanager.googleapis.com"
)

for api in "${apis[@]}"; do
    print_color "  Enabling $api..." "$BLUE"
    gcloud services enable $api || true
done

# Create service account
print_color "Creating service account..." "$YELLOW"
if gcloud iam service-accounts describe $SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com >/dev/null 2>&1; then
    print_color "Service account already exists" "$GREEN"
else
    gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
        --display-name="Nexus Med Service Account" \
        --description="Service account for Nexus Med Cloud Run service"
    print_color "Service account created" "$GREEN"
fi

# Grant necessary roles to service account
print_color "Granting roles to service account..." "$YELLOW"
roles=(
    "roles/run.invoker"
    "roles/secretmanager.secretAccessor"
    "roles/logging.logWriter"
    "roles/monitoring.metricWriter"
)

for role in "${roles[@]}"; do
    print_color "  Granting $role..." "$BLUE"
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
        --role="$role" \
        --quiet || true
done

# Configure Docker for Container Registry
print_color "Configuring Docker for Container Registry..." "$YELLOW"
if command_exists docker; then
    gcloud auth configure-docker --quiet
    print_color "Docker configured for gcr.io" "$GREEN"
else
    print_color "Docker not found. Please install Docker to build images locally." "$YELLOW"
fi

# Set default region for Cloud Run
print_color "Setting default region for Cloud Run..." "$YELLOW"
gcloud config set run/region $REGION

# Create Secret Manager secret placeholder
print_color "Setting up Secret Manager..." "$YELLOW"
echo ""
print_color "To create a secret for your Anthropic API key, run:" "$BLUE"
echo "echo -n 'YOUR_API_KEY' | gcloud secrets create anthropic-api-key --data-file=-"
echo ""

# Check Cloud Build service account
print_color "Checking Cloud Build permissions..." "$YELLOW"
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
CLOUD_BUILD_SA="$<EMAIL>"

print_color "Granting Cloud Run Admin role to Cloud Build..." "$BLUE"
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUD_BUILD_SA" \
    --role="roles/run.admin" \
    --quiet || true

gcloud iam service-accounts add-iam-policy-binding \
    $SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com \
    --member="serviceAccount:$CLOUD_BUILD_SA" \
    --role="roles/iam.serviceAccountUser" \
    --quiet || true

# Summary
print_color "✅ Setup completed successfully!" "$GREEN"
echo ""
print_color "Summary:" "$GREEN"
echo "  Project ID: $PROJECT_ID"
echo "  Region: $REGION"
echo "  Service Account: $SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com"
echo ""
print_color "Next steps:" "$YELLOW"
echo "1. Create your Anthropic API key secret:"
echo "   echo -n 'YOUR_API_KEY' | gcloud secrets create anthropic-api-key --data-file=-"
echo ""
echo "2. Run the deployment script:"
echo "   ./deploy.sh --project-id $PROJECT_ID --api-key YOUR_API_KEY"
echo ""
print_color "🎉 Your project is ready for deployment!" "$GREEN"