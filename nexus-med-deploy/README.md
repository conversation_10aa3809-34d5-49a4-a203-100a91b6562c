# Nexus Med - Cloud Run Deployment

Este projeto contém todos os arquivos necessários para fazer deploy do Nexus Med (House MD PhD) no Google Cloud Run.

## Estrutura do Projeto

```
nexus-med-deploy/
├── Dockerfile          # Imagem Docker para o aplicativo
├── requirements.txt    # Dependências Python
├── .dockerignore      # Arquivos ignorados no build
├── cloudbuild.yaml    # Configuração para Cloud Build
├── service.yaml       # Configuração do serviço Cloud Run
├── deploy.sh          # Script de deploy automatizado
├── setup.sh           # Script de configuração inicial
└── app.py             # Aplicativo (copiado do 37.py)
```

## Pré-requisitos

1. **Google Cloud SDK** instalado e configurado
2. **Docker** instalado (para build local)
3. **Projeto GCP** com billing ativado
4. **API Key da Anthropic** para o Claude

## Deploy Rápido

### 1. Configuração Inicial

```bash
# Clone ou copie os arquivos para um diretório
cd nexus-med-deploy/

# Execute o script de setup (primeira vez apenas)
./setup.sh --project-id SEU_PROJECT_ID
```

### 2. Deploy do Aplicativo

```bash
# Deploy usando o script automatizado
./deploy.sh --project-id SEU_PROJECT_ID --api-key SUA_ANTHROPIC_API_KEY
```

## Deploy Manual

### 1. Configurar o Projeto GCP

```bash
# Definir projeto
gcloud config set project SEU_PROJECT_ID

# Habilitar APIs necessárias
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### 2. Build Local

```bash
# Copiar o arquivo da aplicação
cp ../37.py ./app.py

# Build da imagem
docker build -t gcr.io/SEU_PROJECT_ID/nexus-med:latest .

# Push para Container Registry
docker push gcr.io/SEU_PROJECT_ID/nexus-med:latest
```

### 3. Deploy no Cloud Run

```bash
gcloud run deploy nexus-med \
  --image gcr.io/SEU_PROJECT_ID/nexus-med:latest \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --set-env-vars "ANTHROPIC_API_KEY=SUA_API_KEY"
```

## Deploy com Cloud Build

```bash
# Submeter para Cloud Build
gcloud builds submit \
  --config cloudbuild.yaml \
  --substitutions="_ANTHROPIC_API_KEY=SUA_API_KEY"
```

## Configuração de Secrets (Recomendado)

Para maior segurança, use Google Secret Manager:

```bash
# Criar secret
echo -n "SUA_API_KEY" | gcloud secrets create anthropic-api-key --data-file=-

# Dar permissão ao Cloud Run
gcloud secrets add-iam-policy-binding anthropic-api-key \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

# Deploy com secret
gcloud run deploy nexus-med \
  --image gcr.io/SEU_PROJECT_ID/nexus-med:latest \
  --update-secrets="ANTHROPIC_API_KEY=anthropic-api-key:latest"
```

## Configurações do Serviço

- **Memória**: 2Gi (recomendado para Streamlit)
- **CPU**: 2 vCPUs
- **Timeout**: 300 segundos
- **Instâncias**: Min 1, Max 10
- **Concorrência**: 100 requests por container

## Monitoramento

```bash
# Ver logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=nexus-med" --limit 50

# Ver métricas
gcloud monitoring dashboards list

# Descrever serviço
gcloud run services describe nexus-med --region us-central1
```

## Troubleshooting

### Erro de Memória
Se o app falhar com erro de memória, aumente no deploy:
```bash
--memory 4Gi
```

### Erro de Timeout
Para processos longos, aumente o timeout:
```bash
--timeout 900
```

### Erro de API Key
Verifique se a variável de ambiente está configurada:
```bash
gcloud run services describe nexus-med --region us-central1 --format="value(spec.template.spec.containers[0].env[?(@.name=='ANTHROPIC_API_KEY')])"
```

## Custos Estimados

- **Cloud Run**: ~$0.10-$1.00/dia (com tráfego baixo)
- **Container Registry**: ~$0.10/mês
- **Cloud Build**: ~$0.003/minuto de build

## Segurança

1. **Não commitar API keys** no código
2. Use **Secret Manager** para produção
3. Configure **IAM** apropriadamente
4. Ative **Cloud Armor** se necessário
5. Configure **HTTPS** (automático no Cloud Run)

## Atualizações

Para atualizar o aplicativo:

```bash
# Modificar o código
# Rebuild e redeploy
./deploy.sh --project-id SEU_PROJECT_ID --api-key SUA_API_KEY
```

## Rollback

```bash
# Listar revisões
gcloud run revisions list --service nexus-med --region us-central1

# Rollback para revisão anterior
gcloud run services update-traffic nexus-med \
  --to-revisions=nexus-med-00001-abc=100 \
  --region us-central1
```